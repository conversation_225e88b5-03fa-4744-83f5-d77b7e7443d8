import {
  pgTable,
  text,
  integer,
  index,
  jsonb,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from '../auth/users.schema';
import { resumeDocuments } from '../resume/documents.schema';
import {
  difficultyLevel,
  experienceLevel,
  interviewStatus,
  interviewStyle,
  interviewType,
  meetingType,
  recordingStatus,
} from '../auth/enums';

export const interviews = pgTable(
  'interviews',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),
    userId: text('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
    resumeId: text('resume_id')
      .notNull()
      .references(() => resumeDocuments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    title: text('title'),
    interviewType: interviewType('interview_type').notNull(),
    jobTitle: text('job_title').notNull(),
    companyName: text('company_name').notNull(),
    industryName: text('industry_name').notNull(),
    experienceLevel: experienceLevel('experience_level').notNull(),
    difficultyLevel: difficultyLevel('difficulty_level').notNull(),
    interviewStyle: interviewStyle('interview_style')
      .notNull()
      .default('NEUTRAL'),
    durationMinutes: integer('duration_minutes').notNull().default(60),
    relevantSkills: text('relevant_skills')
      .array()
      .notNull()
      .default(sql`'{}'::text[]`),
    jobDescription: text('job_description'),
    keyResponsibilities: text('key_responsibilities'),
    customInstructions: text('custom_instructions'),
    status: interviewStatus('status').notNull().default('PENDING'),
    sessionData: jsonb('session_data'),
    roomId: text('room_id'),
    recordingUrl: text('recording_url'),
    recordingStatus: recordingStatus('recording_status'),
    roomStartedAt: timestamp('room_started_at', { withTimezone: true }),
    roomEndedAt: timestamp('room_ended_at', { withTimezone: true }),
    meetingType: meetingType('meeting_type').notNull().default('AI_PRACTICE'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    userIdx: index('idx_interviews_user').on(t.userId, t.createdAt),
    resumeIdx: index('idx_interviews_resume').on(t.resumeId),
    statusIdx: index('idx_interviews_status').on(t.status, t.createdAt),
  }),
);
