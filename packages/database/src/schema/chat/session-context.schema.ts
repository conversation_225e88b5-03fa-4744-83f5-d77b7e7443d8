import { sql } from 'drizzle-orm';
import {
  pgTable,
  text,
  boolean,
  jsonb,
  primaryKey,
  index,
} from 'drizzle-orm/pg-core';
import { chatSessions } from './sessions.schema';
import { chatContextItems } from './context-items.schema';

export const chatSessionContext = pgTable(
  'chat_session_context',
  {
    sessionId: text('session_id')
      .notNull()
      .references(() => chatSessions.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    contextItemId: text('context_item_id')
      .notNull()
      .references(() => chatContextItems.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    isPinned: boolean('is_pinned').notNull().default(false),
    overrides: jsonb('overrides')
      .notNull()
      .default(sql`'{}'::jsonb`),
  },
  (t) => ({
    pk: primaryKey({
      columns: [t.sessionId, t.contextItemId],
      name: 'chat_session_context_pk',
    }),
    pinnedIdx: index('idx_session_context_pinned').on(t.isPinned),
  }),
);
