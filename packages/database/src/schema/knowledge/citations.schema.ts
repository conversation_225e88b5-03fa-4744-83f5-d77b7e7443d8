import { sql } from 'drizzle-orm';
import { pgTable, text, real, index } from 'drizzle-orm/pg-core';
import { chatMessages } from '../chat/messages.schema';
import { knowledgeChunks } from './chunks.schema';

export const chatMessageCitations = pgTable(
  'chat_message_citations',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),

    messageId: text('message_id')
      .notNull()
      .references(() => chatMessages.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    chunkId: text('chunk_id')
      .notNull()
      .references(() => knowledgeChunks.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    quote: text('quote'),
    score: real('score'),
  },
  (t) => ({
    messageIdx: index('idx_citations_message').on(t.messageId),
    chunkIdx: index('idx_citations_chunk').on(t.chunkId),
  }),
);
