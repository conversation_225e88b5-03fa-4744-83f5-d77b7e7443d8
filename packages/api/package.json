{"name": "@repo/api", "version": "0.0.0", "private": true, "license": "MIT", "scripts": {"dev": "pnpm build --watch", "build": "tsc -b -v", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\""}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["./dist/**"], "publishConfig": {"access": "public"}, "typesVersions": {"*": {"*": ["src/*"]}}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./*": {"import": "./dist/*.js", "require": "./dist/*.js"}}, "dependencies": {"@nestjs/mapped-types": "*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.10.7", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "typescript": "5.5.4"}}