'use client';

import { useAuth } from '../lib/auth-context';

export default function Home() {
  const {
    user,
    loading,
    error,
    signOut,
    signInWithGoogle,
    signInWithGithub,
    clearError,
  } = useAuth();

  return (
    <div
      style={{
        padding: '40px',
        maxWidth: '500px',
        margin: '0 auto',
        fontFamily: 'system-ui, sans-serif',
        backgroundColor: 'var(--background)',
        color: 'var(--foreground)',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>Looma.ai</h1>

      {loading && (
        <div style={{ textAlign: 'center' }}>
          <p>Loading...</p>
        </div>
      )}

      {user ? (
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: '#28a745', marginBottom: '20px' }}>
            ✅ Welcome, {user.displayName || user.email}!
          </h2>
          <div style={{ marginBottom: '20px' }}>
            <p>
              <strong>Email:</strong> {user.email}
            </p>
            <p>
              <strong>UID:</strong> {user.uid}
            </p>
          </div>

          <button
            onClick={signOut}
            style={{
              padding: '12px 24px',
              margin: '10px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              display: 'block',
              width: '100%',
            }}
          >
            🚪 Sign Out
          </button>

          <button
            onClick={async () => {
              try {
                const token = await user.getIdToken();
                const response = await fetch(
                  `/${process.env.NEXT_PUBLIC_API_URL}/users/me`,
                  {
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  },
                );
                const data = await response.json();
                console.log('✅ Backend response:', data);
              } catch (error) {
                console.error('❌ Backend error:', error);
              }
            }}
            style={{
              padding: '12px 24px',
              margin: '10px',
              backgroundColor: '#17a2b8',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              display: 'block',
              width: '100%',
            }}
          >
            🔗 Test Authenticated Backend Call
          </button>
        </div>
      ) : (
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>
            🔐 Please Sign In
          </h2>

          {error && (
            <div
              style={{
                backgroundColor: '#f8d7da',
                color: '#721c24',
                padding: '10px',
                borderRadius: '8px',
                marginBottom: '20px',
                border: '1px solid #f5c6cb',
              }}
            >
              <p style={{ margin: 0 }}>{error}</p>
              <button
                onClick={clearError}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#721c24',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                  fontSize: '12px',
                  marginTop: '5px',
                }}
              >
                Dismiss
              </button>
            </div>
          )}

          <div style={{ marginBottom: '20px' }}>
            <button
              onClick={async () => {
                try {
                  await signInWithGoogle(false); // popup
                } catch (error) {
                  console.error('Popup sign-in failed:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#db4437',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔑 Sign In with Google (Popup)
            </button>

            <button
              onClick={async () => {
                try {
                  await signInWithGoogle(true); // redirect
                } catch (error) {
                  console.error('Redirect sign-in failed:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#4285f4',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔄 Sign In with Google (Redirect)
            </button>

            <button
              onClick={async () => {
                try {
                  await signInWithGithub(false); // popup
                } catch (error) {
                  console.error('GitHub sign-in failed:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#333',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔑 Sign In with GitHub (Popup)
            </button>

            <button
              onClick={async () => {
                try {
                  await signInWithGithub(true); // redirect
                } catch (error) {
                  console.error('GitHub redirect sign-in failed:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#24292e',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔄 Sign In with GitHub (Redirect)
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
