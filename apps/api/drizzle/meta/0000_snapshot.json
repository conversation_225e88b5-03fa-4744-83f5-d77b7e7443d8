{"id": "4fba04a1-8486-45a5-b65f-6fe17a99834a", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USER'"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "display_username": {"name": "display_username", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_accounts_user": {"name": "idx_accounts_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "uniq_provider_account": {"name": "uniq_provider_account", "columns": [{"expression": "provider_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"idx_sessions_user_expires": {"name": "idx_sessions_user_expires", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resume_documents": {"name": "resume_documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "raw_text": {"name": "raw_text", "type": "text", "primaryKey": false, "notNull": false}, "ai_summary": {"name": "ai_summary", "type": "text", "primaryKey": false, "notNull": false}, "extracted_skills": {"name": "extracted_skills", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "uploaded_at": {"name": "uploaded_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_resume_user": {"name": "idx_resume_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"resume_documents_user_id_users_id_fk": {"name": "resume_documents_user_id_users_id_fk", "tableFrom": "resume_documents", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resume_sections": {"name": "resume_sections", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "resume_id": {"name": "resume_id", "type": "text", "primaryKey": false, "notNull": true}, "section_type": {"name": "section_type", "type": "resume_section_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"idx_resume_sections_resume_pos": {"name": "idx_resume_sections_resume_pos", "columns": [{"expression": "resume_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "position", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"resume_sections_resume_id_resume_documents_id_fk": {"name": "resume_sections_resume_id_resume_documents_id_fk", "tableFrom": "resume_sections", "tableTo": "resume_documents", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resume_skills": {"name": "resume_skills", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "resume_id": {"name": "resume_id", "type": "text", "primaryKey": false, "notNull": true}, "skill_name": {"name": "skill_name", "type": "text", "primaryKey": false, "notNull": true}, "proficiency": {"name": "proficiency", "type": "skill_proficiency", "typeSchema": "public", "primaryKey": false, "notNull": false}, "years_experience": {"name": "years_experience", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"idx_resume_skills_resume_name": {"name": "idx_resume_skills_resume_name", "columns": [{"expression": "resume_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "skill_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"resume_skills_resume_id_resume_documents_id_fk": {"name": "resume_skills_resume_id_resume_documents_id_fk", "tableFrom": "resume_skills", "tableTo": "resume_documents", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interviews": {"name": "interviews", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "resume_id": {"name": "resume_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "interview_type": {"name": "interview_type", "type": "interview_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true}, "industry_name": {"name": "industry_name", "type": "text", "primaryKey": false, "notNull": true}, "experience_level": {"name": "experience_level", "type": "experience_level", "typeSchema": "public", "primaryKey": false, "notNull": true}, "difficulty_level": {"name": "difficulty_level", "type": "difficulty_level", "typeSchema": "public", "primaryKey": false, "notNull": true}, "interview_style": {"name": "interview_style", "type": "interview_style", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'NEUTRAL'"}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "relevant_skills": {"name": "relevant_skills", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "job_description": {"name": "job_description", "type": "text", "primaryKey": false, "notNull": false}, "key_responsibilities": {"name": "key_responsibilities", "type": "text", "primaryKey": false, "notNull": false}, "custom_instructions": {"name": "custom_instructions", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "interview_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "session_data": {"name": "session_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "room_id": {"name": "room_id", "type": "text", "primaryKey": false, "notNull": false}, "recording_url": {"name": "recording_url", "type": "text", "primaryKey": false, "notNull": false}, "recording_status": {"name": "recording_status", "type": "recording_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "room_started_at": {"name": "room_started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "room_ended_at": {"name": "room_ended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "meeting_type": {"name": "meeting_type", "type": "meeting_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'AI_PRACTICE'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_interviews_user": {"name": "idx_interviews_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_interviews_resume": {"name": "idx_interviews_resume", "columns": [{"expression": "resume_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_interviews_status": {"name": "idx_interviews_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interviews_user_id_users_id_fk": {"name": "interviews_user_id_users_id_fk", "tableFrom": "interviews", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "interviews_resume_id_resume_documents_id_fk": {"name": "interviews_resume_id_resume_documents_id_fk", "tableFrom": "interviews", "tableTo": "resume_documents", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_participants": {"name": "interview_participants", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "participant_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "identity": {"name": "identity", "type": "text", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "left_at": {"name": "left_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "meta": {"name": "meta", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {"idx_interview_participants_interview": {"name": "idx_interview_participants_interview", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interview_participants_interview_id_interviews_id_fk": {"name": "interview_participants_interview_id_interviews_id_fk", "tableFrom": "interview_participants", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_questions": {"name": "interview_questions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "question_text": {"name": "question_text", "type": "text", "primaryKey": false, "notNull": true}, "ideal_answer": {"name": "ideal_answer", "type": "text", "primaryKey": false, "notNull": false}, "answer_summary": {"name": "answer_summary", "type": "text", "primaryKey": false, "notNull": false}, "score": {"name": "score", "type": "real", "primaryKey": false, "notNull": false}, "rubric": {"name": "rubric", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "ended_at": {"name": "ended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_interview_questions_interview_pos": {"name": "idx_interview_questions_interview_pos", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "position", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interview_questions_interview_id_interviews_id_fk": {"name": "interview_questions_interview_id_interviews_id_fk", "tableFrom": "interview_questions", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_utterances": {"name": "interview_utterances", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "question_id": {"name": "question_id", "type": "text", "primaryKey": false, "notNull": false}, "speaker": {"name": "speaker", "type": "speaker_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "transcript": {"name": "transcript", "type": "text", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "start_ms": {"name": "start_ms", "type": "integer", "primaryKey": false, "notNull": true}, "end_ms": {"name": "end_ms", "type": "integer", "primaryKey": false, "notNull": true}, "word_count": {"name": "word_count", "type": "integer", "primaryKey": false, "notNull": false}, "metrics": {"name": "metrics", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_utterances_interview_time": {"name": "idx_utterances_interview_time", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_ms", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_utterances_question": {"name": "idx_utterances_question", "columns": [{"expression": "question_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interview_utterances_interview_id_interviews_id_fk": {"name": "interview_utterances_interview_id_interviews_id_fk", "tableFrom": "interview_utterances", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "interview_utterances_question_id_interview_questions_id_fk": {"name": "interview_utterances_question_id_interview_questions_id_fk", "tableFrom": "interview_utterances", "tableTo": "interview_questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_media_assets": {"name": "interview_media_assets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "utterance_id": {"name": "utterance_id", "type": "text", "primaryKey": false, "notNull": false}, "media_type": {"name": "media_type", "type": "media_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "duration_ms": {"name": "duration_ms", "type": "integer", "primaryKey": false, "notNull": false}, "format": {"name": "format", "type": "text", "primaryKey": false, "notNull": false}, "size_bytes": {"name": "size_bytes", "type": "integer", "primaryKey": false, "notNull": false}, "meta": {"name": "meta", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_media_interview": {"name": "idx_media_interview", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_utterance": {"name": "idx_media_utterance", "columns": [{"expression": "utterance_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interview_media_assets_interview_id_interviews_id_fk": {"name": "interview_media_assets_interview_id_interviews_id_fk", "tableFrom": "interview_media_assets", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "interview_media_assets_utterance_id_interview_utterances_id_fk": {"name": "interview_media_assets_utterance_id_interview_utterances_id_fk", "tableFrom": "interview_media_assets", "tableTo": "interview_utterances", "columnsFrom": ["utterance_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_room_events": {"name": "interview_room_events", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {"idx_room_events_interview_at": {"name": "idx_room_events_interview_at", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interview_room_events_interview_id_interviews_id_fk": {"name": "interview_room_events_interview_id_interviews_id_fk", "tableFrom": "interview_room_events", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_feedback": {"name": "interview_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "overall_score": {"name": "overall_score", "type": "real", "primaryKey": false, "notNull": false}, "communication_score": {"name": "communication_score", "type": "real", "primaryKey": false, "notNull": false}, "technical_depth_score": {"name": "technical_depth_score", "type": "real", "primaryKey": false, "notNull": false}, "problem_solving_score": {"name": "problem_solving_score", "type": "real", "primaryKey": false, "notNull": false}, "domain_knowledge_score": {"name": "domain_knowledge_score", "type": "real", "primaryKey": false, "notNull": false}, "strengths": {"name": "strengths", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "weaknesses": {"name": "weaknesses", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "improvement_areas": {"name": "improvement_areas", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "metrics": {"name": "metrics", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "recommendations": {"name": "recommendations", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "rubric_version": {"name": "rubric_version", "type": "text", "primaryKey": false, "notNull": false}, "generated_at": {"name": "generated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"interview_feedback_interview_id_interviews_id_fk": {"name": "interview_feedback_interview_id_interviews_id_fk", "tableFrom": "interview_feedback", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"interview_feedback_interview_id_unique": {"name": "interview_feedback_interview_id_unique", "nullsNotDistinct": false, "columns": ["interview_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_skill_ratings": {"name": "interview_skill_ratings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": true}, "skill_name": {"name": "skill_name", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "real", "primaryKey": false, "notNull": false}, "evidence": {"name": "evidence", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_skill_ratings_interview_skill": {"name": "idx_skill_ratings_interview_skill", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "skill_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interview_skill_ratings_interview_id_interviews_id_fk": {"name": "interview_skill_ratings_interview_id_interviews_id_fk", "tableFrom": "interview_skill_ratings", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "resume_id": {"name": "resume_id", "type": "text", "primaryKey": false, "notNull": false}, "interview_id": {"name": "interview_id", "type": "text", "primaryKey": false, "notNull": false}, "session_type": {"name": "session_type", "type": "chat_session_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'GENERAL'"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_chat_sessions_user": {"name": "idx_chat_sessions_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_chat_sessions_resume": {"name": "idx_chat_sessions_resume", "columns": [{"expression": "resume_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_chat_sessions_interview": {"name": "idx_chat_sessions_interview", "columns": [{"expression": "interview_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_sessions_user_id_users_id_fk": {"name": "chat_sessions_user_id_users_id_fk", "tableFrom": "chat_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "chat_sessions_resume_id_resume_documents_id_fk": {"name": "chat_sessions_resume_id_resume_documents_id_fk", "tableFrom": "chat_sessions", "tableTo": "resume_documents", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "chat_sessions_interview_id_interviews_id_fk": {"name": "chat_sessions_interview_id_interviews_id_fk", "tableFrom": "chat_sessions", "tableTo": "interviews", "columnsFrom": ["interview_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "message_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_chat_messages_session_time": {"name": "idx_chat_messages_session_time", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_messages_session_id_chat_sessions_id_fk": {"name": "chat_messages_session_id_chat_sessions_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_tool_events": {"name": "chat_tool_events", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "tool_name": {"name": "tool_name", "type": "text", "primaryKey": false, "notNull": true}, "call_type": {"name": "call_type", "type": "tool_call_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'OTHER'"}, "request": {"name": "request", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "response": {"name": "response", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "latency_ms": {"name": "latency_ms", "type": "real", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_tool_events_message": {"name": "idx_tool_events_message", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_tool_events_message_id_chat_messages_id_fk": {"name": "chat_tool_events_message_id_chat_messages_id_fk", "tableFrom": "chat_tool_events", "tableTo": "chat_messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_context_items": {"name": "chat_context_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "knowledge_source", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'NOTE'"}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_context_items_user_active": {"name": "idx_context_items_user_active", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_context_items_user_id_users_id_fk": {"name": "chat_context_items_user_id_users_id_fk", "tableFrom": "chat_context_items", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_session_context": {"name": "chat_session_context", "schema": "", "columns": {"session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "context_item_id": {"name": "context_item_id", "type": "text", "primaryKey": false, "notNull": true}, "is_pinned": {"name": "is_pinned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "overrides": {"name": "overrides", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {"idx_session_context_pinned": {"name": "idx_session_context_pinned", "columns": [{"expression": "is_pinned", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_session_context_session_id_chat_sessions_id_fk": {"name": "chat_session_context_session_id_chat_sessions_id_fk", "tableFrom": "chat_session_context", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "chat_session_context_context_item_id_chat_context_items_id_fk": {"name": "chat_session_context_context_item_id_chat_context_items_id_fk", "tableFrom": "chat_session_context", "tableTo": "chat_context_items", "columnsFrom": ["context_item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"chat_session_context_pk": {"name": "chat_session_context_pk", "columns": ["session_id", "context_item_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_message_context": {"name": "chat_message_context", "schema": "", "columns": {"message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "context_item_id": {"name": "context_item_id", "type": "text", "primaryKey": false, "notNull": true}, "relevance_score": {"name": "relevance_score", "type": "real", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_message_context_message_id_chat_messages_id_fk": {"name": "chat_message_context_message_id_chat_messages_id_fk", "tableFrom": "chat_message_context", "tableTo": "chat_messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "chat_message_context_context_item_id_chat_context_items_id_fk": {"name": "chat_message_context_context_item_id_chat_context_items_id_fk", "tableFrom": "chat_message_context", "tableTo": "chat_context_items", "columnsFrom": ["context_item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"chat_message_context_pk": {"name": "chat_message_context_pk", "columns": ["message_id", "context_item_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.knowledge_documents": {"name": "knowledge_documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "resume_id": {"name": "resume_id", "type": "text", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "knowledge_source", "typeSchema": "public", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "chunk_count": {"name": "chunk_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "external_vector_namespace": {"name": "external_vector_namespace", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_kdocs_user": {"name": "idx_kdocs_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_kdocs_resume": {"name": "idx_kdocs_resume", "columns": [{"expression": "resume_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_kdocs_namespace": {"name": "idx_kdocs_namespace", "columns": [{"expression": "external_vector_namespace", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"knowledge_documents_user_id_users_id_fk": {"name": "knowledge_documents_user_id_users_id_fk", "tableFrom": "knowledge_documents", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "knowledge_documents_resume_id_resume_documents_id_fk": {"name": "knowledge_documents_resume_id_resume_documents_id_fk", "tableFrom": "knowledge_documents", "tableTo": "resume_documents", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.knowledge_chunks": {"name": "knowledge_chunks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": true}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "external_vector_id": {"name": "external_vector_id", "type": "text", "primaryKey": false, "notNull": false}, "meta": {"name": "meta", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_kchunks_document_pos": {"name": "idx_kchunks_document_pos", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "position", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_kchunks_vector_id": {"name": "idx_kchunks_vector_id", "columns": [{"expression": "external_vector_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"knowledge_chunks_document_id_knowledge_documents_id_fk": {"name": "knowledge_chunks_document_id_knowledge_documents_id_fk", "tableFrom": "knowledge_chunks", "tableTo": "knowledge_documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"knowledge_chunks_hash_unique": {"name": "knowledge_chunks_hash_unique", "nullsNotDistinct": false, "columns": ["hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_message_citations": {"name": "chat_message_citations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "chunk_id": {"name": "chunk_id", "type": "text", "primaryKey": false, "notNull": true}, "quote": {"name": "quote", "type": "text", "primaryKey": false, "notNull": false}, "score": {"name": "score", "type": "real", "primaryKey": false, "notNull": false}}, "indexes": {"idx_citations_message": {"name": "idx_citations_message", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_citations_chunk": {"name": "idx_citations_chunk", "columns": [{"expression": "chunk_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_message_citations_message_id_chat_messages_id_fk": {"name": "chat_message_citations_message_id_chat_messages_id_fk", "tableFrom": "chat_message_citations", "tableTo": "chat_messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "chat_message_citations_chunk_id_knowledge_chunks_id_fk": {"name": "chat_message_citations_chunk_id_knowledge_chunks_id_fk", "tableFrom": "chat_message_citations", "tableTo": "knowledge_chunks", "columnsFrom": ["chunk_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.chat_session_type": {"name": "chat_session_type", "schema": "public", "values": ["RESUME_REVIEW", "INTERVIEW_PREP", "INTERVIEW_RESULTS", "GENERAL"]}, "public.difficulty_level": {"name": "difficulty_level", "schema": "public", "values": ["INTRODUCTORY", "BASIC", "INTERMEDIATE", "ADVANCED", "EXPERT"]}, "public.experience_level": {"name": "experience_level", "schema": "public", "values": ["ENTRY", "JUNIOR", "MID", "SENIOR", "LEAD", "EXECUTIVE"]}, "public.interview_status": {"name": "interview_status", "schema": "public", "values": ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"]}, "public.interview_style": {"name": "interview_style", "schema": "public", "values": ["FRIENDLY", "NEUTRAL", "CHALLENGING"]}, "public.interview_type": {"name": "interview_type", "schema": "public", "values": ["CODING", "SYSTEM_DESIGN", "TECHNICAL_CONCEPTS", "BEHAVIORAL", "CASE_STUDY", "DOMAIN_SPECIFIC"]}, "public.knowledge_source": {"name": "knowledge_source", "schema": "public", "values": ["RESUME", "LINKEDIN", "FILE", "NOTE", "URL", "EXTERNAL"]}, "public.media_type": {"name": "media_type", "schema": "public", "values": ["AUDIO", "VIDEO"]}, "public.meeting_type": {"name": "meeting_type", "schema": "public", "values": ["AI_PRACTICE", "LIVE_INTERVIEW", "PEER_PRACTICE"]}, "public.message_role": {"name": "message_role", "schema": "public", "values": ["USER", "ASSISTANT", "SYSTEM"]}, "public.participant_role": {"name": "participant_role", "schema": "public", "values": ["INTERVIEWER_AI", "CANDIDATE", "INTERVIEWER_HUMAN", "PEER"]}, "public.recording_status": {"name": "recording_status", "schema": "public", "values": ["NOT_STARTED", "RECORDING", "PROCESSING", "COMPLETED", "FAILED"]}, "public.resume_section_type": {"name": "resume_section_type", "schema": "public", "values": ["SUMMARY", "EXPERIENCE", "EDUCATION", "PROJECTS", "SKILLS", "OTHER"]}, "public.skill_proficiency": {"name": "skill_proficiency", "schema": "public", "values": ["NOVICE", "INTERMEDIATE", "ADVANCED", "EXPERT"]}, "public.speaker_role": {"name": "speaker_role", "schema": "public", "values": ["INTERVIEWER", "CANDIDATE", "SYSTEM"]}, "public.tool_call_type": {"name": "tool_call_type", "schema": "public", "values": ["RETRIEVE", "FUNCTION_CALL", "WEB_SEARCH", "OTHER"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["USER", "ADMIN"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}