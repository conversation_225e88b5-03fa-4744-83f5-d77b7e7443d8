import { pgTable, text, timestamp, unique, boolean, index, uniqueIndex, foreignKey, integer, jsonb, real, primaryKey, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const chatSessionType = pgEnum("chat_session_type", ['RESUME_REVIEW', 'INTERVIEW_PREP', 'INTERVIEW_RESULTS', 'GENERAL'])
export const difficultyLevel = pgEnum("difficulty_level", ['INTRODUCTORY', 'BASIC', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'])
export const experienceLevel = pgEnum("experience_level", ['ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'LEAD', 'EXECUTIVE'])
export const interviewStatus = pgEnum("interview_status", ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'])
export const interviewStyle = pgEnum("interview_style", ['FRIENDLY', 'NEUTRAL', 'CHALLENGING'])
export const interviewType = pgEnum("interview_type", ['CODING', 'SYSTEM_DESIGN', 'TECHNICAL_CONCEPTS', 'BEHAVIORAL', 'CASE_STUDY', 'DOMAIN_SPECIFIC'])
export const knowledgeSource = pgEnum("knowledge_source", ['RESUME', 'LINKEDIN', 'FILE', 'NOTE', 'URL', 'EXTERNAL'])
export const mediaType = pgEnum("media_type", ['AUDIO', 'VIDEO'])
export const meetingType = pgEnum("meeting_type", ['AI_PRACTICE', 'LIVE_INTERVIEW', 'PEER_PRACTICE'])
export const messageRole = pgEnum("message_role", ['USER', 'ASSISTANT', 'SYSTEM'])
export const participantRole = pgEnum("participant_role", ['INTERVIEWER_AI', 'CANDIDATE', 'INTERVIEWER_HUMAN', 'PEER'])
export const recordingStatus = pgEnum("recording_status", ['NOT_STARTED', 'RECORDING', 'PROCESSING', 'COMPLETED', 'FAILED'])
export const resumeSectionType = pgEnum("resume_section_type", ['SUMMARY', 'EXPERIENCE', 'EDUCATION', 'PROJECTS', 'SKILLS', 'OTHER'])
export const skillProficiency = pgEnum("skill_proficiency", ['NOVICE', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'])
export const speakerRole = pgEnum("speaker_role", ['INTERVIEWER', 'CANDIDATE', 'SYSTEM'])
export const toolCallType = pgEnum("tool_call_type", ['RETRIEVE', 'FUNCTION_CALL', 'WEB_SEARCH', 'OTHER'])
export const userRole = pgEnum("user_role", ['USER', 'ADMIN'])


export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const users = pgTable("users", {
	id: text().primaryKey().notNull(),
	email: text().notNull(),
	name: text().notNull(),
	bio: text(),
	role: userRole().default('USER').notNull(),
	emailVerified: boolean("email_verified").notNull(),
	image: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	stripeCustomerId: text("stripe_customer_id"),
	password: text(),
	username: text().notNull(),
	displayUsername: text("display_username"),
}, (table) => [
	unique("users_email_unique").on(table.email),
]);

export const accounts = pgTable("accounts", {
	id: text().primaryKey().notNull(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id").notNull(),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at", { withTimezone: true, mode: 'string' }),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at", { withTimezone: true, mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_accounts_user").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	uniqueIndex("uniq_provider_account").using("btree", table.providerId.asc().nullsLast().op("text_ops"), table.accountId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "accounts_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const sessions = pgTable("sessions", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }).notNull(),
	token: text().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id").notNull(),
}, (table) => [
	index("idx_sessions_user_expires").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.expiresAt.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "sessions_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	unique("sessions_token_unique").on(table.token),
]);

export const resumeDocuments = pgTable("resume_documents", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	title: text(),
	fileName: text("file_name").notNull(),
	fileUrl: text("file_url").notNull(),
	fileType: text("file_type").notNull(),
	fileSize: integer("file_size").notNull(),
	rawText: text("raw_text"),
	aiSummary: text("ai_summary"),
	extractedSkills: text("extracted_skills").array().default([""]).notNull(),
	uploadedAt: timestamp("uploaded_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	processedAt: timestamp("processed_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("idx_resume_user").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "resume_documents_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const resumeSections = pgTable("resume_sections", {
	id: text().primaryKey().notNull(),
	resumeId: text("resume_id").notNull(),
	sectionType: resumeSectionType("section_type").notNull(),
	content: text().notNull(),
	position: integer().notNull(),
}, (table) => [
	index("idx_resume_sections_resume_pos").using("btree", table.resumeId.asc().nullsLast().op("int4_ops"), table.position.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.resumeId],
			foreignColumns: [resumeDocuments.id],
			name: "resume_sections_resume_id_resume_documents_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const resumeSkills = pgTable("resume_skills", {
	id: text().primaryKey().notNull(),
	resumeId: text("resume_id").notNull(),
	skillName: text("skill_name").notNull(),
	proficiency: skillProficiency(),
	yearsExperience: integer("years_experience"),
}, (table) => [
	index("idx_resume_skills_resume_name").using("btree", table.resumeId.asc().nullsLast().op("text_ops"), table.skillName.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.resumeId],
			foreignColumns: [resumeDocuments.id],
			name: "resume_skills_resume_id_resume_documents_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const interviews = pgTable("interviews", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	resumeId: text("resume_id").notNull(),
	title: text(),
	interviewType: interviewType("interview_type").notNull(),
	jobTitle: text("job_title").notNull(),
	companyName: text("company_name").notNull(),
	industryName: text("industry_name").notNull(),
	experienceLevel: experienceLevel("experience_level").notNull(),
	difficultyLevel: difficultyLevel("difficulty_level").notNull(),
	interviewStyle: interviewStyle("interview_style").default('NEUTRAL').notNull(),
	durationMinutes: integer("duration_minutes").default(60).notNull(),
	relevantSkills: text("relevant_skills").array().default([""]).notNull(),
	jobDescription: text("job_description"),
	keyResponsibilities: text("key_responsibilities"),
	customInstructions: text("custom_instructions"),
	status: interviewStatus().default('PENDING').notNull(),
	sessionData: jsonb("session_data"),
	roomId: text("room_id"),
	recordingUrl: text("recording_url"),
	recordingStatus: recordingStatus("recording_status"),
	roomStartedAt: timestamp("room_started_at", { withTimezone: true, mode: 'string' }),
	roomEndedAt: timestamp("room_ended_at", { withTimezone: true, mode: 'string' }),
	meetingType: meetingType("meeting_type").default('AI_PRACTICE').notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_interviews_resume").using("btree", table.resumeId.asc().nullsLast().op("text_ops")),
	index("idx_interviews_status").using("btree", table.status.asc().nullsLast().op("enum_ops"), table.createdAt.asc().nullsLast().op("enum_ops")),
	index("idx_interviews_user").using("btree", table.userId.asc().nullsLast().op("timestamptz_ops"), table.createdAt.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "interviews_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.resumeId],
			foreignColumns: [resumeDocuments.id],
			name: "interviews_resume_id_resume_documents_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const interviewParticipants = pgTable("interview_participants", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	role: participantRole().notNull(),
	identity: text().notNull(),
	joinedAt: timestamp("joined_at", { withTimezone: true, mode: 'string' }),
	leftAt: timestamp("left_at", { withTimezone: true, mode: 'string' }),
	meta: jsonb().default({}).notNull(),
}, (table) => [
	index("idx_interview_participants_interview").using("btree", table.interviewId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_participants_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const interviewQuestions = pgTable("interview_questions", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	position: integer().notNull(),
	questionText: text("question_text").notNull(),
	idealAnswer: text("ideal_answer"),
	answerSummary: text("answer_summary"),
	score: real(),
	rubric: jsonb().default({}).notNull(),
	startedAt: timestamp("started_at", { withTimezone: true, mode: 'string' }),
	endedAt: timestamp("ended_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("idx_interview_questions_interview_pos").using("btree", table.interviewId.asc().nullsLast().op("int4_ops"), table.position.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_questions_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const interviewUtterances = pgTable("interview_utterances", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	questionId: text("question_id"),
	speaker: speakerRole().notNull(),
	transcript: text(),
	audioUrl: text("audio_url"),
	startMs: integer("start_ms").notNull(),
	endMs: integer("end_ms").notNull(),
	wordCount: integer("word_count"),
	metrics: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_utterances_interview_time").using("btree", table.interviewId.asc().nullsLast().op("int4_ops"), table.startMs.asc().nullsLast().op("int4_ops")),
	index("idx_utterances_question").using("btree", table.questionId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_utterances_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.questionId],
			foreignColumns: [interviewQuestions.id],
			name: "interview_utterances_question_id_interview_questions_id_fk"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const interviewMediaAssets = pgTable("interview_media_assets", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	utteranceId: text("utterance_id"),
	mediaType: mediaType("media_type").notNull(),
	url: text().notNull(),
	durationMs: integer("duration_ms"),
	format: text(),
	sizeBytes: integer("size_bytes"),
	meta: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_media_interview").using("btree", table.interviewId.asc().nullsLast().op("text_ops")),
	index("idx_media_utterance").using("btree", table.utteranceId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_media_assets_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.utteranceId],
			foreignColumns: [interviewUtterances.id],
			name: "interview_media_assets_utterance_id_interview_utterances_id_fk"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const interviewRoomEvents = pgTable("interview_room_events", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	eventType: text("event_type").notNull(),
	at: timestamp({ withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	data: jsonb().default({}).notNull(),
}, (table) => [
	index("idx_room_events_interview_at").using("btree", table.interviewId.asc().nullsLast().op("text_ops"), table.at.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_room_events_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const interviewFeedback = pgTable("interview_feedback", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	overallScore: real("overall_score"),
	communicationScore: real("communication_score"),
	technicalDepthScore: real("technical_depth_score"),
	problemSolvingScore: real("problem_solving_score"),
	domainKnowledgeScore: real("domain_knowledge_score"),
	strengths: text().array().default([""]).notNull(),
	weaknesses: text().array().default([""]).notNull(),
	improvementAreas: text("improvement_areas").array().default([""]).notNull(),
	metrics: jsonb().default({}).notNull(),
	recommendations: jsonb().default({}).notNull(),
	rubricVersion: text("rubric_version"),
	generatedAt: timestamp("generated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_feedback_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	unique("interview_feedback_interview_id_unique").on(table.interviewId),
]);

export const interviewSkillRatings = pgTable("interview_skill_ratings", {
	id: text().primaryKey().notNull(),
	interviewId: text("interview_id").notNull(),
	skillName: text("skill_name").notNull(),
	score: real(),
	evidence: text(),
}, (table) => [
	index("idx_skill_ratings_interview_skill").using("btree", table.interviewId.asc().nullsLast().op("text_ops"), table.skillName.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "interview_skill_ratings_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const chatSessions = pgTable("chat_sessions", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	resumeId: text("resume_id"),
	interviewId: text("interview_id"),
	sessionType: chatSessionType("session_type").default('GENERAL').notNull(),
	title: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_chat_sessions_interview").using("btree", table.interviewId.asc().nullsLast().op("text_ops")),
	index("idx_chat_sessions_resume").using("btree", table.resumeId.asc().nullsLast().op("text_ops")),
	index("idx_chat_sessions_user").using("btree", table.userId.asc().nullsLast().op("timestamptz_ops"), table.createdAt.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "chat_sessions_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.resumeId],
			foreignColumns: [resumeDocuments.id],
			name: "chat_sessions_resume_id_resume_documents_id_fk"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.interviewId],
			foreignColumns: [interviews.id],
			name: "chat_sessions_interview_id_interviews_id_fk"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const chatMessages = pgTable("chat_messages", {
	id: text().primaryKey().notNull(),
	sessionId: text("session_id").notNull(),
	role: messageRole().notNull(),
	content: text().notNull(),
	metadata: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_chat_messages_session_time").using("btree", table.sessionId.asc().nullsLast().op("text_ops"), table.createdAt.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.sessionId],
			foreignColumns: [chatSessions.id],
			name: "chat_messages_session_id_chat_sessions_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const chatToolEvents = pgTable("chat_tool_events", {
	id: text().primaryKey().notNull(),
	messageId: text("message_id").notNull(),
	toolName: text("tool_name").notNull(),
	callType: toolCallType("call_type").default('OTHER').notNull(),
	request: jsonb().default({}).notNull(),
	response: jsonb().default({}).notNull(),
	latencyMs: real("latency_ms"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_tool_events_message").using("btree", table.messageId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [chatMessages.id],
			name: "chat_tool_events_message_id_chat_messages_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const chatContextItems = pgTable("chat_context_items", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	title: text(),
	content: text(),
	source: knowledgeSource().default('NOTE').notNull(),
	version: integer().default(1).notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	metadata: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_context_items_user_active").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.isActive.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "chat_context_items_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const knowledgeDocuments = pgTable("knowledge_documents", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	resumeId: text("resume_id"),
	source: knowledgeSource().notNull(),
	title: text(),
	url: text(),
	chunkCount: integer("chunk_count").default(0).notNull(),
	externalVectorNamespace: text("external_vector_namespace"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_kdocs_namespace").using("btree", table.externalVectorNamespace.asc().nullsLast().op("text_ops")),
	index("idx_kdocs_resume").using("btree", table.resumeId.asc().nullsLast().op("text_ops")),
	index("idx_kdocs_user").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "knowledge_documents_user_id_users_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.resumeId],
			foreignColumns: [resumeDocuments.id],
			name: "knowledge_documents_resume_id_resume_documents_id_fk"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const knowledgeChunks = pgTable("knowledge_chunks", {
	id: text().primaryKey().notNull(),
	documentId: text("document_id").notNull(),
	position: integer().notNull(),
	content: text().notNull(),
	hash: text().notNull(),
	tokenCount: integer("token_count"),
	externalVectorId: text("external_vector_id"),
	meta: jsonb().default({}).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_kchunks_document_pos").using("btree", table.documentId.asc().nullsLast().op("text_ops"), table.position.asc().nullsLast().op("int4_ops")),
	index("idx_kchunks_vector_id").using("btree", table.externalVectorId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.documentId],
			foreignColumns: [knowledgeDocuments.id],
			name: "knowledge_chunks_document_id_knowledge_documents_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	unique("knowledge_chunks_hash_unique").on(table.hash),
]);

export const chatMessageCitations = pgTable("chat_message_citations", {
	id: text().primaryKey().notNull(),
	messageId: text("message_id").notNull(),
	chunkId: text("chunk_id").notNull(),
	quote: text(),
	score: real(),
}, (table) => [
	index("idx_citations_chunk").using("btree", table.chunkId.asc().nullsLast().op("text_ops")),
	index("idx_citations_message").using("btree", table.messageId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [chatMessages.id],
			name: "chat_message_citations_message_id_chat_messages_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.chunkId],
			foreignColumns: [knowledgeChunks.id],
			name: "chat_message_citations_chunk_id_knowledge_chunks_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const chatMessageContext = pgTable("chat_message_context", {
	messageId: text("message_id").notNull(),
	contextItemId: text("context_item_id").notNull(),
	relevanceScore: real("relevance_score"),
}, (table) => [
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [chatMessages.id],
			name: "chat_message_context_message_id_chat_messages_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.contextItemId],
			foreignColumns: [chatContextItems.id],
			name: "chat_message_context_context_item_id_chat_context_items_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	primaryKey({ columns: [table.messageId, table.contextItemId], name: "chat_message_context_pk"}),
]);

export const chatSessionContext = pgTable("chat_session_context", {
	sessionId: text("session_id").notNull(),
	contextItemId: text("context_item_id").notNull(),
	isPinned: boolean("is_pinned").default(false).notNull(),
	overrides: jsonb().default({}).notNull(),
}, (table) => [
	index("idx_session_context_pinned").using("btree", table.isPinned.asc().nullsLast().op("bool_ops")),
	foreignKey({
			columns: [table.sessionId],
			foreignColumns: [chatSessions.id],
			name: "chat_session_context_session_id_chat_sessions_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.contextItemId],
			foreignColumns: [chatContextItems.id],
			name: "chat_session_context_context_item_id_chat_context_items_id_fk"
		}).onUpdate("cascade").onDelete("cascade"),
	primaryKey({ columns: [table.sessionId, table.contextItemId], name: "chat_session_context_pk"}),
]);
