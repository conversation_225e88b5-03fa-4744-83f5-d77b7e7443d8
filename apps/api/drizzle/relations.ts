import { relations } from "drizzle-orm/relations";
import { users, accounts, sessions, resumeDocuments, resumeSections, resumeSkills, interviews, interviewParticipants, interviewQuestions, interviewUtterances, interviewMediaAssets, interviewRoomEvents, interviewFeedback, interviewSkillRatings, chatSessions, chatMessages, chatToolEvents, chatContextItems, knowledgeDocuments, knowledgeChunks, chatMessageCitations, chatMessageContext, chatSessionContext } from "./schema";

export const accountsRelations = relations(accounts, ({one}) => ({
	user: one(users, {
		fields: [accounts.userId],
		references: [users.id]
	}),
}));

export const usersRelations = relations(users, ({many}) => ({
	accounts: many(accounts),
	sessions: many(sessions),
	resumeDocuments: many(resumeDocuments),
	interviews: many(interviews),
	chatSessions: many(chatSessions),
	chatContextItems: many(chatContextItems),
	knowledgeDocuments: many(knowledgeDocuments),
}));

export const sessionsRelations = relations(sessions, ({one}) => ({
	user: one(users, {
		fields: [sessions.userId],
		references: [users.id]
	}),
}));

export const resumeDocumentsRelations = relations(resumeDocuments, ({one, many}) => ({
	user: one(users, {
		fields: [resumeDocuments.userId],
		references: [users.id]
	}),
	resumeSections: many(resumeSections),
	resumeSkills: many(resumeSkills),
	interviews: many(interviews),
	chatSessions: many(chatSessions),
	knowledgeDocuments: many(knowledgeDocuments),
}));

export const resumeSectionsRelations = relations(resumeSections, ({one}) => ({
	resumeDocument: one(resumeDocuments, {
		fields: [resumeSections.resumeId],
		references: [resumeDocuments.id]
	}),
}));

export const resumeSkillsRelations = relations(resumeSkills, ({one}) => ({
	resumeDocument: one(resumeDocuments, {
		fields: [resumeSkills.resumeId],
		references: [resumeDocuments.id]
	}),
}));

export const interviewsRelations = relations(interviews, ({one, many}) => ({
	user: one(users, {
		fields: [interviews.userId],
		references: [users.id]
	}),
	resumeDocument: one(resumeDocuments, {
		fields: [interviews.resumeId],
		references: [resumeDocuments.id]
	}),
	interviewParticipants: many(interviewParticipants),
	interviewQuestions: many(interviewQuestions),
	interviewUtterances: many(interviewUtterances),
	interviewMediaAssets: many(interviewMediaAssets),
	interviewRoomEvents: many(interviewRoomEvents),
	interviewFeedbacks: many(interviewFeedback),
	interviewSkillRatings: many(interviewSkillRatings),
	chatSessions: many(chatSessions),
}));

export const interviewParticipantsRelations = relations(interviewParticipants, ({one}) => ({
	interview: one(interviews, {
		fields: [interviewParticipants.interviewId],
		references: [interviews.id]
	}),
}));

export const interviewQuestionsRelations = relations(interviewQuestions, ({one, many}) => ({
	interview: one(interviews, {
		fields: [interviewQuestions.interviewId],
		references: [interviews.id]
	}),
	interviewUtterances: many(interviewUtterances),
}));

export const interviewUtterancesRelations = relations(interviewUtterances, ({one, many}) => ({
	interview: one(interviews, {
		fields: [interviewUtterances.interviewId],
		references: [interviews.id]
	}),
	interviewQuestion: one(interviewQuestions, {
		fields: [interviewUtterances.questionId],
		references: [interviewQuestions.id]
	}),
	interviewMediaAssets: many(interviewMediaAssets),
}));

export const interviewMediaAssetsRelations = relations(interviewMediaAssets, ({one}) => ({
	interview: one(interviews, {
		fields: [interviewMediaAssets.interviewId],
		references: [interviews.id]
	}),
	interviewUtterance: one(interviewUtterances, {
		fields: [interviewMediaAssets.utteranceId],
		references: [interviewUtterances.id]
	}),
}));

export const interviewRoomEventsRelations = relations(interviewRoomEvents, ({one}) => ({
	interview: one(interviews, {
		fields: [interviewRoomEvents.interviewId],
		references: [interviews.id]
	}),
}));

export const interviewFeedbackRelations = relations(interviewFeedback, ({one}) => ({
	interview: one(interviews, {
		fields: [interviewFeedback.interviewId],
		references: [interviews.id]
	}),
}));

export const interviewSkillRatingsRelations = relations(interviewSkillRatings, ({one}) => ({
	interview: one(interviews, {
		fields: [interviewSkillRatings.interviewId],
		references: [interviews.id]
	}),
}));

export const chatSessionsRelations = relations(chatSessions, ({one, many}) => ({
	user: one(users, {
		fields: [chatSessions.userId],
		references: [users.id]
	}),
	resumeDocument: one(resumeDocuments, {
		fields: [chatSessions.resumeId],
		references: [resumeDocuments.id]
	}),
	interview: one(interviews, {
		fields: [chatSessions.interviewId],
		references: [interviews.id]
	}),
	chatMessages: many(chatMessages),
	chatSessionContexts: many(chatSessionContext),
}));

export const chatMessagesRelations = relations(chatMessages, ({one, many}) => ({
	chatSession: one(chatSessions, {
		fields: [chatMessages.sessionId],
		references: [chatSessions.id]
	}),
	chatToolEvents: many(chatToolEvents),
	chatMessageCitations: many(chatMessageCitations),
	chatMessageContexts: many(chatMessageContext),
}));

export const chatToolEventsRelations = relations(chatToolEvents, ({one}) => ({
	chatMessage: one(chatMessages, {
		fields: [chatToolEvents.messageId],
		references: [chatMessages.id]
	}),
}));

export const chatContextItemsRelations = relations(chatContextItems, ({one, many}) => ({
	user: one(users, {
		fields: [chatContextItems.userId],
		references: [users.id]
	}),
	chatMessageContexts: many(chatMessageContext),
	chatSessionContexts: many(chatSessionContext),
}));

export const knowledgeDocumentsRelations = relations(knowledgeDocuments, ({one, many}) => ({
	user: one(users, {
		fields: [knowledgeDocuments.userId],
		references: [users.id]
	}),
	resumeDocument: one(resumeDocuments, {
		fields: [knowledgeDocuments.resumeId],
		references: [resumeDocuments.id]
	}),
	knowledgeChunks: many(knowledgeChunks),
}));

export const knowledgeChunksRelations = relations(knowledgeChunks, ({one, many}) => ({
	knowledgeDocument: one(knowledgeDocuments, {
		fields: [knowledgeChunks.documentId],
		references: [knowledgeDocuments.id]
	}),
	chatMessageCitations: many(chatMessageCitations),
}));

export const chatMessageCitationsRelations = relations(chatMessageCitations, ({one}) => ({
	chatMessage: one(chatMessages, {
		fields: [chatMessageCitations.messageId],
		references: [chatMessages.id]
	}),
	knowledgeChunk: one(knowledgeChunks, {
		fields: [chatMessageCitations.chunkId],
		references: [knowledgeChunks.id]
	}),
}));

export const chatMessageContextRelations = relations(chatMessageContext, ({one}) => ({
	chatMessage: one(chatMessages, {
		fields: [chatMessageContext.messageId],
		references: [chatMessages.id]
	}),
	chatContextItem: one(chatContextItems, {
		fields: [chatMessageContext.contextItemId],
		references: [chatContextItems.id]
	}),
}));

export const chatSessionContextRelations = relations(chatSessionContext, ({one}) => ({
	chatSession: one(chatSessions, {
		fields: [chatSessionContext.sessionId],
		references: [chatSessions.id]
	}),
	chatContextItem: one(chatContextItems, {
		fields: [chatSessionContext.contextItemId],
		references: [chatContextItems.id]
	}),
}));