import * as admin from 'firebase-admin';

// Check if Firebase environment variables are set
const hasFirebaseConfig =
  process.env.FIREBASE_PROJECT_ID &&
  process.env.FIREBASE_PRIVATE_KEY &&
  process.env.FIREBASE_CLIENT_EMAIL;

let firebaseAdmin: typeof admin | null = null;
let auth: admin.auth.Auth | null = null;

if (hasFirebaseConfig) {
  const firebaseConfig = {
    projectId: process.env.FIREBASE_PROJECT_ID,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  };

  // Initialize Firebase Admin SDK
  if (!admin.apps.length) {
    try {
      admin.initializeApp({
        credential: admin.credential.cert(firebaseConfig),
        projectId: firebaseConfig.projectId,
      });
      firebaseAdmin = admin;
      auth = admin.auth();
      console.log('✅ Firebase Admin SDK initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase Admin SDK:', error);
    }
  } else {
    firebaseAdmin = admin;
    auth = admin.auth();
  }
} else {
  console.warn(
    '⚠️ Firebase environment variables not set. Firebase authentication will not work.',
  );
}

export { firebaseAdmin, auth };
