import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';

import { LinksModule } from './links/links.module';
import { AppService } from './app.service';
import { AppController } from './app.controller';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './users/users.module';
import { FirebaseAuthGuard } from './auth/firebase-auth.guard';

@Module({
  imports: [LinksModule, UsersModule, ConfigModule.forRoot(), DatabaseModule],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: FirebaseAuthGuard,
    },
  ],
})
export class AppModule {}
