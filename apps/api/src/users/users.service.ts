import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { DRIZZLE } from '../database/database.module';
import { users } from '@repo/database/schema';
import { plainToClass } from 'class-transformer';
import { UserResponseDto } from './dto/user-response.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(@Inject(DRIZZLE) private db: any) {}

  async createFromFirebase(firebaseUser: any): Promise<UserResponseDto> {
    const existingUser = await this.findByEmail(firebaseUser.email);
    if (existingUser) {
      return existingUser;
    }

    const newUser = await this.db
      .insert(users)
      .values({
        id: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.name || firebaseUser.email.split('@')[0],
        username: firebaseUser.email.split('@')[0],
        emailVerified: firebaseUser.email_verified || false,
        image: firebaseUser.picture,
      })
      .returning();

    return plainToClass(UserResponseDto, newUser[0]);
  }

  async findByEmail(email: string): Promise<UserResponseDto | null> {
    const user = await this.db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);
    return user.length > 0 ? plainToClass(UserResponseDto, user[0]) : null;
  }

  async findById(id: string): Promise<UserResponseDto> {
    const user = await this.db
      .select()
      .from(users)
      .where(eq(users.email, id))
      .limit(1);
    if (user.length === 0) {
      throw new NotFoundException('User not found');
    }
    return plainToClass(UserResponseDto, user[0]);
  }

  async update(
    id: string,
    updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    const updatedUser = await this.db
      .update(users)
      .set({ ...updateUserDto, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();

    if (updatedUser.length === 0) {
      throw new NotFoundException('User not found');
    }

    return plainToClass(UserResponseDto, updatedUser[0]);
  }

  async remove(id: string): Promise<void> {
    const result = await this.db
      .delete(users)
      .where(eq(users.id, id))
      .returning();
    if (result.length === 0) {
      throw new NotFoundException('User not found');
    }
  }
}
