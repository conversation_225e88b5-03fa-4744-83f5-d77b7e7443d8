import { IsS<PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Url, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  bio?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  username?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  displayUsername?: string;

  @IsOptional()
  @IsUrl()
  image?: string;
}
