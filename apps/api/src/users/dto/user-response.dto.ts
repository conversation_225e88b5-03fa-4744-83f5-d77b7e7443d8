import { Exclude, Expose } from 'class-transformer';

export class UserResponseDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  name: string;

  @Expose()
  bio?: string;

  @Expose()
  role: string;

  @Expose()
  emailVerified: boolean;

  @Expose()
  image?: string;

  @Expose()
  username: string;

  @Expose()
  displayUsername?: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  @Exclude()
  password?: string;

  @Exclude()
  stripeCustomerId?: string;
}
