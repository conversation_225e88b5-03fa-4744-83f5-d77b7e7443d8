import { Exclude, Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({ description: 'User ID' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'User email' })
  @Expose()
  email: string;

  @ApiProperty({ description: 'User full name' })
  @Expose()
  name: string;

  @ApiProperty({ description: 'User bio', required: false })
  @Expose()
  bio?: string;

  @ApiProperty({ description: 'User role', enum: ['USER', 'ADMIN'] })
  @Expose()
  role: string;

  @ApiProperty({ description: 'Email verification status' })
  @Expose()
  emailVerified: boolean;

  @ApiProperty({ description: 'Profile image URL', required: false })
  @Expose()
  image?: string;

  @ApiProperty({ description: 'Username' })
  @Expose()
  username: string;

  @ApiProperty({ description: 'Display username', required: false })
  @Expose()
  displayUsername?: string;

  @ApiProperty({ description: 'Account creation date' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  @Expose()
  updatedAt: Date;

  @Exclude()
  password?: string;

  @Exclude()
  stripeCustomerId?: string;
}
