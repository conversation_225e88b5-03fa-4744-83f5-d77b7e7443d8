import { Controller, Get, UseGuards } from '@nestjs/common';
import { FirebaseAuthGuard } from '../auth/firebase-auth.guard';
import { FirebaseUser } from '../auth/firebase-user.decorator';

@Controller('users')
export class UsersController {
  @Get('me')
  @UseGuards(FirebaseAuthGuard)
  async getProfile(@FirebaseUser() user: any) {
    return {
      user: {
        uid: user.uid,
        email: user.email,
        name: user.name,
        picture: user.picture,
      },
    };
  }
}
