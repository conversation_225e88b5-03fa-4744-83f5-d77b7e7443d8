import { Controller, Get, Put, Body, Delete } from '@nestjs/common';
import { FirebaseUser } from '../auth/firebase-user.decorator';
import { UsersService } from './users.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  async getProfile(@FirebaseUser() user: any): Promise<UserResponseDto> {
    // Ensure user exists in DB, create if not
    return await this.usersService.createFromFirebase(user);
  }

  @Put('me')
  async updateProfile(
    @FirebaseUser() user: any,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    return await this.usersService.update(user.uid, updateUserDto);
  }

  @Delete('me')
  async deleteProfile(@FirebaseUser() user: any): Promise<{ message: string }> {
    await this.usersService.remove(user.uid);
    return { message: 'User deleted successfully' };
  }
}
